class SessionsController < Devise::SessionsController
  AuthorizationHeaderError = Class.new(StandardError)
  UserNotFoundError = Class.new(StandardError)
  IdentityNotMatchError = Class.new(StandardError)

  include AuthRedirect
  include Redirectable
  include VerifySameOrigin
  include Cookie<PERSON>elper
  include Authentication::FusionAuthHelper

  layout "plain"
  component :session

  # The "Sign Out" link needs to work from a global navbar (not just inside murmur).
  # This means it cannot support standard CSRF token verification, so we simply validate the same origin instead (similar to CORS).
  replace_csrf_with_origin_check(only: [:destroy])

  prepend_before_action :impersonate_any_user_test_hook
  prepend_before_action :impersonate_anonymous_user_test_hook

  skip_before_action :show_privacy_notice_for_new_user, only: [:destroy]
  skip_before_action :disable_diversity_and_inclusion_freemium_access, only: [:destroy]

  expose(:page_subdomain) { account_label_from_hostname }

  def new
    # This SAML login redirect won't work on a non-production environment due to the request
    # subdomain not being the account subdomain (eg. lilbub.cultureamp.com instead of google.cultureamp.com).
    return redirect_to_saml_sign_in(saml_subdomain) if force_saml_sign_in?

    if account_authentication_option(account_from_subdomain) == :saml
      redirect_to_saml_sign_in(account_label_from_hostname)
    elsif new_auth_rollout_redirect?
      redirect_to auth_path(new_auth_return_path_params)
    else
      set_return_to
      super
    end
  end

  def create
    return super unless user

    case user_authentication_option(user)
    when :email
      super do
        track_sign_in_event(auth_method: Analytics::AUTH_METHOD_PASSWORD)
      end
    when :saml
      Rails.logger.info "Redirecting SessionsController#create to a SAML sign in for subdomain '#{user.account.subdomain}'"
      redirect_to_saml_sign_in(user.account.subdomain)
    when :none
      flash.alert = "Email authentication has been disabled. Please check with your Culture Amp administrator or email #{Appamp::Settings.email_support}."
      redirect_to new_user_session_path
    end
  end

  def new_sign_in
    id_info = jwt_payload
    effective_user_id = id_info["effectiveUserId"]

    if effective_user_id.nil?
      Rails.logger.error "FusionAuth Authorization JWT is invalid - missing effectiveUserId - '#{jwt}'"
    end

    user = Person.find_employee_by_aggregate_id(effective_user_id)

    raise(UserNotFoundError, "User is not found with aggregate_id #{effective_user_id}") unless user

    return render_forbidden unless enable_new_sign_in?(user)

    validate_identity(id_info, user)

    unless fusionauth_jwt_post_signin_enabled?(user)
      sign_in user

      session[:created_at] = Time.current.utc
    end

    Rails.logger.info "Sign in user: #{effective_user_id} with FusionAuth Authorization header."

    track_sign_in_event(
      auth_method: auth_method(id_info["authenticationType"]),
      auth_system: Analytics::AUTHENTICATION_SYSTEM_FUSION_AUTH
    )

    redirect_to after_sign_in_path_for(user)
  rescue AuthorizationHeaderError, UserNotFoundError, IdentityNotMatchError => e
    Sentry.capture_exception(e)
    render_error
  end

  def impersonate_any_user_test_hook
    # only acts as a hook for testing, no-op for any other environment
  end

  def impersonate_anonymous_user_test_hook
    # only acts as a hook for testing, no-op for any other environment
  end

  def google_auth_error
    flash.alert = "That email address doesn't match a Culture Amp account. Please try again or contact your administrator"
    Rails.logger.info "Failed to authorize via FusionAuth"

    redirect_to new_user_session_path
  end

  def destroy
    if fusionauth_jwt_post_signin_enabled?(current_user)
      sign_out_from_fusionauth
    else
      super
    end
  end

  protected

  def after_sign_out_path_for(_resource)
    "https://www.cultureamp.com/sign_out"
  end

  private


  def require_no_authentication
    if fusionauth_jwt_post_signin_enabled?
      
  end

  def account_authentication_option(account)
    return :none unless account

    if allow_email_login?(account)
      :email
    elsif account.active_saml?
      :saml
    else
      :none
    end
  end

  def user_authentication_option(user)
    # If the user has not set a password and SAML is enabled on the account then redirect them to their IdP
    if user.encrypted_password.nil? && user.account && SAML::Authenticator.valid_saml_account?(user.account) == :ok
      :saml
    else
      account_authentication_option(user.account)
    end
  end

  # This user is unauthenticated - do not trust
  def user
    user_email = params.dig(:user, :email)

    @user ||= user_email && Person.email(user_email)
  end

  def force_saml_sign_in?
    params.dig("account", "force_saml_sign_in").present?
  end

  def saml_subdomain
    params.dig("account", "subdomain")
  end

  def redirect_to_saml_sign_in(subdomain)
    redir = session[:return_to] || params[:redirect] || "/"
    redirect_to saml_signin_path(sd: subdomain, redirect: redir)
  end

  def allow_email_login?(account)
    account_authenticator(account).email_allowed?
  end

  def account_authenticator(account)
    AccountProperties::Authenticator.for_account(account_id: account.id)
  end

  def auth_method(authentication_type)
    return Analytics::AUTH_METHOD_PASSWORD if authentication_type == "PASSWORD"
    return Analytics::AUTH_METHOD_GOOGLE if authentication_type == "GOOGLE"
  end

  def validate_identity(id_info, user)
    account_id = id_info["accountId"]
    effective_user_id = id_info["effectiveUserId"]

    if user.aggregate_id != effective_user_id || user.account.aggregate_id != account_id
      raise(IdentityNotMatchError, "Identity for FusionAuth Authorization JWT (account_id: #{account_id}, effective_user_id: #{effective_user_id}) is not match with user #{user.aggregate_id}")
    end
  end

  def enable_new_sign_in?(user)
    FeatureFlags::Queries::ValueForAccount.new.call(
      account_aggregate_id: user&.account&.aggregate_id,
      flag_name: FeatureFlags::Flags::SIGN_IN_WITH_NEW_AUTH_JWT,
      fallback_value: false
    )
  end

  def new_auth_return_path_params
    {redirect: session[:return_to] || params[:redirect]}.compact
  end

  def sign_out_from_fusionauth
    refresh_token = cookies[fa_refresh_token_cookie_name]

    delete_fusionauth_entity(jwt_sid)
    sign_out_fusionauth(user_id: current_user.aggregate_id, refresh_token: refresh_token)
    remove_cookie(fa_jwt_cookie_name)
    remove_cookie(fa_refresh_token_cookie_name)
  end
end
